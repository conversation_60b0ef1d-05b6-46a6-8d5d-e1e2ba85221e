extends Node

# Preload the debug panel scene
const DebugPanelScene = preload("res://scenes/ui/DebugPanel.tscn")

var debug_panel_instance = null

func _ready():
	Logger.info("Game", "MainGame.gd _ready() called.")
	# Instance and add the debug panel only in debug builds
	if OS.is_debug_build():
		Logger.debug("Game", "Debug build detected. Instancing DebugPanel.")
		
		# Set default position for debug builds
		GpsService.set_gps_simulation(GpsService.gps_provider == null)
		GpsService.set_simulated_location(48.9747357, 14.474285) # ČB

		debug_panel_instance = DebugPanelScene.instantiate()
		# Add it as a child. Ensure it draws on top if needed,
		# potentially by adding it to a CanvasLayer or setting z-index.
		# For simplicity, adding directly first. Adjust if needed.
		add_child(debug_panel_instance)
		# Initially hide it, let the user toggle it
		debug_panel_instance.hide()
	else:
		Logger.debug("Game", "Release build detected. DebugPanel not instanced.")


# Changed from _input to _unhandled_input
func _unhandled_input(event):
	# --- Debug Print ---
	# print("Unhandled Input event received: ", event)
	
	# Check if the debug panel exists and if the toggle key (e.g., Semicolon) is pressed
	if event is InputEventKey and event.pressed:
		# --- Debug Print ---
		Logger.debug("Input", "Unhandled Key pressed: %s | Debug Panel Instance valid? %s" % [event.keycode, is_instance_valid(debug_panel_instance)])
		
		if is_instance_valid(debug_panel_instance) and event.keycode == KEY_SEMICOLON:
			Logger.debug("Input", "Semicolon key pressed (unhandled), toggling DebugPanel visibility.")
			debug_panel_instance.toggle_visibility()
			# Prevent the event from propagating further if needed
			# Note: set_input_as_handled() might not be needed in _unhandled_input
			# depending on desired behavior, but keep it for now.
			get_viewport().set_input_as_handled()
